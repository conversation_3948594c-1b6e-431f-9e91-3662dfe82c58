import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import {
  AppB<PERSON>,
  Too<PERSON><PERSON>,
  Typo<PERSON>,
  Button,
  Container,
  Paper,
  Box,
  Chip,
  Alert,
  Snackbar,
  Fade,
  ThemeProvider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Checkbox,
  IconButton,
  CircularProgress
} from '@mui/material';
import {
  VideoLibrary,
  PlayArrow,
  AccountCircle,
  Logout,
  CheckCircle,
  CheckCircleOutline,
  Close
} from '@mui/icons-material';
import * as signalR from "@microsoft/signalr";
import { BrowserRouter as Router, Route, Routes } from 'react-router-dom';
import Subscribe from './Subscribe';
import AuthCallback from './AuthCallback';
import { ThemeProvider as CustomThemeProvider, useTheme } from './contexts/ThemeContext';
import { getTheme } from './theme/themes';
import ThemeSelector from './components/ThemeSelector';
import GoogleSignInButton from './components/GoogleSignInButton';
import GooglePhotosButton from './components/GooglePhotosButton';
import CompressionStatusIndicator, { CompressionStatus } from './components/CompressionStatusIndicator';
import ThemeAwareLogo from './components/ThemeAwareLogo';
import OperationsSidebar from './components/OperationsSidebar';
import JobsPanel from './components/JobsPanel';
import { JobData } from './components/JobsPanel';
import GallerySortControls from './components/GallerySortControls';

// Main App component that uses the theme context
const AppContent: React.FC = () => {
  const { effectiveTheme } = useTheme();
  const theme = getTheme(effectiveTheme);

interface MediaItem {
  id: string;
  filename: string;
  mimeType: string;
  baseUrl: string;
  googlePhotosUrl?: string;
  fileSizeBytes?: number;
  mediaMetadata?: {
    width: string;
    height: string;
    creationTime: string;
    video?: {
      fps: number;
      status: string;
    };
  };
}

// Utility function to format file sizes
const formatFileSize = (bytes?: number): string => {
  if (!bytes) return '';

  const units = ['B', 'KB', 'MB', 'GB'];
  let size = bytes;
  let unitIndex = 0;

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }

  return `${size.toFixed(unitIndex === 0 ? 0 : 1)} ${units[unitIndex]}`;
};

// Function to sort media items based on the selected sort option
const getSortedMediaItems = useCallback((items: MediaItem[], sortBy: 'none' | 'size' | 'date'): MediaItem[] => {
  if (sortBy === 'none') {
    return items; // Return original order
  }

  return [...items].sort((a, b) => {
    if (sortBy === 'size') {
      const sizeA = a.fileSizeBytes || 0;
      const sizeB = b.fileSizeBytes || 0;
      return sizeB - sizeA; // Largest first
    }

    if (sortBy === 'date') {
      const dateA = a.mediaMetadata?.creationTime ? new Date(a.mediaMetadata.creationTime).getTime() : 0;
      const dateB = b.mediaMetadata?.creationTime ? new Date(b.mediaMetadata.creationTime).getTime() : 0;
      return dateB - dateA; // Newest first
    }

    return 0;
  });
}, []);

// Utility function to estimate file size based on dimensions and media type
const estimateFileSize = (mediaItem: MediaItem): number => {
  const width = mediaItem.mediaMetadata?.width ? parseInt(mediaItem.mediaMetadata.width) : 1920;
  const height = mediaItem.mediaMetadata?.height ? parseInt(mediaItem.mediaMetadata.height) : 1080;
  const pixels = width * height;

  if (mediaItem.mimeType?.startsWith('video/')) {
    // Video size estimation: assume 30fps, 60 seconds, moderate compression
    const fps = mediaItem.mediaMetadata?.video?.fps || 30;
    const durationSeconds = 60; // Default estimate
    const bitsPerPixel = 0.1; // Moderate compression
    return Math.round(pixels * fps * durationSeconds * bitsPerPixel / 8);
  } else {
    // Photo size estimation: assume JPEG compression
    const bitsPerPixel = 2; // JPEG compression ratio
    return Math.round(pixels * bitsPerPixel / 8);
  }
};

  const [token, setToken] = useState<string | null>(localStorage.getItem('jwt'));
  const [mediaItems, setMediaItems] = useState<MediaItem[]>([]);
  const [notification, setNotification] = useState<string | null>(null);
  const [notificationType, setNotificationType] = useState<'success' | 'error' | 'info'>('info');
  const [videosLoading, setVideosLoading] = useState(false);
  const [cancelPolling, setCancelPolling] = useState(false);
  const cancelPollingRef = useRef(false);
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [lastSelectedIndex, setLastSelectedIndex] = useState<number | null>(null);
  const [compressionModalOpen, setCompressionModalOpen] = useState(false);
  const [sortBy, setSortBy] = useState<'none' | 'size' | 'date'>('none');
  const [compressionTarget, setCompressionTarget] = useState<MediaItem | 'batch' | null>(null);
  const [compressionSettings, setCompressionSettings] = useState({
    quality: 'medium',
    uploadToGooglePhotos: true
  });
  const [compressionStatuses, setCompressionStatuses] = useState<Map<string, CompressionStatus>>(new Map());
  const [expiredMediaItems, setExpiredMediaItems] = useState<Set<string>>(new Set());
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [showInstructions, setShowInstructions] = useState(true);
  const [loadingPastMedia, setLoadingPastMedia] = useState(false);
  const [jobsPanelCollapsed, setJobsPanelCollapsed] = useState(true);
  const [jobs, setJobs] = useState<JobData[]>([]);
  // loadingJobs removed - jobs panel updates via SignalR now

  // Load media items from database when component mounts or token changes
  useEffect(() => {
    if (token) {
      loadMediaItemsFromDatabase();
      handleRefreshJobs(); // Also load jobs when token is available
    }
  }, [token]);

  // Auto-open jobs panel when jobs first appear (but allow manual minimization)
  const [hasSeenJobs, setHasSeenJobs] = useState(false);
  useEffect(() => {
    if (jobs.length > 0 && !hasSeenJobs && jobsPanelCollapsed) {
      console.log("Auto-opening jobs panel - found", jobs.length, "jobs");
      setJobsPanelCollapsed(false);
      setHasSeenJobs(true);
    } else if (jobs.length === 0) {
      // Reset when no jobs remain
      setHasSeenJobs(false);
    }
  }, [jobs.length, hasSeenJobs, jobsPanelCollapsed]);

  // Helper function to handle expired media items
  const handleMediaExpired = (mediaItemId: string) => {
    setExpiredMediaItems(prev => new Set(Array.from(prev).concat(mediaItemId)));
  };

  // Helper function to remove expired media items
  const removeExpiredMediaItems = async (mediaItemIds: string[]) => {
    if (!token) return;

    try {
      // Remove from database
      for (const mediaItemId of mediaItemIds) {
        await removeMediaItemFromDatabase(mediaItemId);
      }

      // Remove from local state
      setMediaItems(prev => prev.filter(item => !mediaItemIds.includes(item.id)));
      setExpiredMediaItems(prev => {
        const newSet = new Set(prev);
        mediaItemIds.forEach(id => newSet.delete(id));
        return newSet;
      });
      setSelectedItems(prev => {
        const newSet = new Set(prev);
        mediaItemIds.forEach(id => newSet.delete(id));
        return newSet;
      });

      showNotification(`Removed ${mediaItemIds.length} expired media item${mediaItemIds.length > 1 ? 's' : ''}`, 'success');
    } catch (error) {
      console.error('Error removing expired media items:', error);
      showNotification('Failed to remove expired media items', 'error');
    }
  };

  // Helper function to get status message
  const getStatusMessage = (status: string): string => {
    switch (status) {
      case 'Queued': return 'Queued for processing';
      case 'DownloadingFromGooglePhotos': return 'Downloading from Google Photos';
      case 'UploadingToStorage': return 'Uploading to cloud storage';
      case 'TranscodingInProgress': return 'Video transcoding in progress';
      case 'CompressingImage': return 'Compressing image';
      case 'DownloadingFromStorage': return 'Downloading compressed media';
      case 'ReadyForBatchUpload': return 'Ready for upload';
      case 'UploadingToGooglePhotos': return 'Uploading to Google Photos';
      case 'Completed': return 'Compression completed';
      case 'Failed': return 'Compression failed';
      case 'Cancelled': return 'Compression cancelled';
      // Legacy statuses for backward compatibility
      case 'Processing': return 'Processing...';
      case 'Uploading': return 'Uploading to Google Photos...';
      case 'UploadCompleted': return 'Upload completed';
      case 'UploadFailed': return 'Upload failed';
      default: return status;
    }
  };

  // Helper function to convert enum number to status string (for backward compatibility)
  const getStatusFromEnum = (statusEnum: number): string => {
    const statusMap: { [key: number]: string } = {
      0: 'Queued',
      1: 'DownloadingFromGooglePhotos',
      2: 'UploadingToStorage',
      3: 'TranscodingInProgress',
      4: 'CompressingImage',
      5: 'DownloadingFromStorage',
      6: 'ReadyForBatchUpload',
      7: 'UploadingToGooglePhotos',
      8: 'Completed',
      9: 'Failed',
      10: 'Cancelled'
    };
    return statusMap[statusEnum] || 'Unknown';
  };

  // Helper function to get progress percentage based on status
  const getProgressPercentage = (status: string): number => {
    switch (status) {
      case 'Queued': return 0;
      case 'DownloadingFromGooglePhotos': return 15;
      case 'UploadingToStorage': return 25;
      case 'TranscodingInProgress': return 50;
      case 'CompressingImage': return 50;
      case 'DownloadingFromStorage': return 70;
      case 'ReadyForBatchUpload': return 80;
      case 'UploadingToGooglePhotos': return 90;
      case 'Completed': return 100;
      case 'Failed': return 0;
      case 'Cancelled': return 0;
      // Legacy statuses
      case 'Processing': return 50;
      case 'Uploading': return 90;
      case 'UploadCompleted': return 100;
      case 'UploadFailed': return 0;
      default: return 0;
    }
  };

  // Helper function for showing notifications
  const showNotification = (message: string, type: 'success' | 'error' | 'info' = 'info') => {
    setNotification(message);
    setNotificationType(type);
  };

  // Helper function to get current user ID from JWT token
  const getCurrentUserId = () => {
    if (!token) return null;
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.sub || payload.nameid || payload.userId;
    } catch {
      return null;
    }
  };

  // Function to refresh token from localStorage (called by AuthCallback)
  const refreshToken = () => {
    const newToken = localStorage.getItem('jwt');
    setToken(newToken);
  };

  // Selection handling functions
  const handleItemClick = (mediaItem: MediaItem, index: number, event: React.MouseEvent) => {
    // Prevent default browser selection behavior
    event.preventDefault();

    const itemId = mediaItem.id;

    if (event.shiftKey && lastSelectedIndex !== null) {
      // Shift-click: select range
      const start = Math.min(lastSelectedIndex, index);
      const end = Math.max(lastSelectedIndex, index);
      const newSelected = new Set(selectedItems);

      for (let i = start; i <= end; i++) {
        if (sortedMediaItems[i]?.id) {
          newSelected.add(sortedMediaItems[i].id);
        }
      }

      setSelectedItems(newSelected);
    } else if (event.ctrlKey || event.metaKey) {
      // Ctrl/Cmd-click: toggle individual item
      const newSelected = new Set(selectedItems);
      if (newSelected.has(itemId)) {
        newSelected.delete(itemId);
      } else {
        newSelected.add(itemId);
      }
      setSelectedItems(newSelected);
      setLastSelectedIndex(index);
    } else {
      // Regular click: handle selection/deselection or open in Google Photos
      const newSelected = new Set(selectedItems);

      if (selectedItems.has(itemId) && selectedItems.size === 1) {
        // If this is the only selected item, open in Google Photos if URL is available
        if (mediaItem.googlePhotosUrl) {
          window.open(mediaItem.googlePhotosUrl, '_blank');
        } else {
          // If no Google Photos URL, deselect the item
          newSelected.clear();
          setLastSelectedIndex(null);
          setSelectedItems(newSelected);
        }
      } else {
        // Otherwise, select only this item
        newSelected.clear();
        newSelected.add(itemId);
        setLastSelectedIndex(index);
        setSelectedItems(newSelected);
      }
    }
  };

  // Memoize sorted items to avoid recalculating on every render
  const sortedMediaItems = useMemo(() => getSortedMediaItems(mediaItems, sortBy), [mediaItems, sortBy, getSortedMediaItems]);

  const handleSelectAll = () => {
    const allIds = sortedMediaItems.map(item => item.id).filter(Boolean);
    setSelectedItems(new Set(allIds));
  };

  const handleClearSelection = () => {
    setSelectedItems(new Set());
    setLastSelectedIndex(null);
  };

  const handleClearAllExpired = () => {
    const expiredIds = Array.from(expiredMediaItems);
    removeExpiredMediaItems(expiredIds);
  };

  // Make refreshToken available globally for AuthCallback
  useEffect(() => {
    (window as any).refreshToken = refreshToken;
    return () => {
      delete (window as any).refreshToken;
    };
  }, []);

  useEffect(() => {
    if (!token) return;

    // Test backend connectivity first
    const testBackendConnectivity = async () => {
      try {
        const response = await fetch('/api/media/jobs?page=1&pageSize=1', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        console.log("Backend connectivity test:", response.status);
        return response.ok;
      } catch (err) {
        console.error("Backend connectivity test failed:", err);
        return false;
      }
    };

    // Use absolute URL to bypass proxy issues with WebSockets
    const hubUrl = "http://localhost:5119/notificationHub";

    console.log("SignalR connecting to:", hubUrl);

    // Test backend connectivity
    testBackendConnectivity();

    const connection = new signalR.HubConnectionBuilder()
      .withUrl(hubUrl, {
        accessTokenFactory: () => token || "",
        transport: signalR.HttpTransportType.WebSockets | signalR.HttpTransportType.LongPolling,
        withCredentials: true
      })
      .configureLogging(signalR.LogLevel.Information)
      .build();

    connection.on("ReceiveMessage", (user, message) => {
      console.log("Received message:", user, message);
      setNotification(`${user}: ${message}`);
    });

    connection.on("TestResponse", (message) => {
      console.log("Test response received:", message);
    });

    connection.on("CompressionStatusUpdate", (statusUpdate) => {
      console.log("🔄 Compression status update received:", statusUpdate);
      console.log("Current user ID:", getCurrentUserId());
      console.log("Status update user ID:", statusUpdate.userId);

      // Only update if this status update is for our user or if it's a broadcast
      if (!statusUpdate.userId || statusUpdate.userId === getCurrentUserId()) {
        console.log("✅ Processing status update for current user");

        setCompressionStatuses(prev => {
          const newMap = new Map(prev);
          newMap.set(statusUpdate.mediaItemId, {
            jobId: statusUpdate.jobId,
            status: statusUpdate.status,
            message: statusUpdate.message,
            progress: statusUpdate.progress,
            error: statusUpdate.error
          });
          console.log("📊 Updated compression statuses:", Object.fromEntries(newMap));
          return newMap;
        });

        // Always refresh jobs panel when status changes (real-time updates)
        console.log("🔄 Refreshing jobs panel due to status update");
        handleRefreshJobs(false);

        // Auto-open jobs panel when new job starts (only if user hasn't seen jobs yet)
        if (statusUpdate.status === 'Processing' && jobsPanelCollapsed && !hasSeenJobs) {
          console.log("📂 Auto-opening jobs panel for new job");
          setJobsPanelCollapsed(false);
          setHasSeenJobs(true);
        }
      } else {
        console.log("❌ Ignoring status update - not for current user");
      }
    });

    // Listen for job list updates (when jobs are created, deleted, etc.)
    connection.on("JobListUpdate", (updateData) => {
      console.log("📋 Job list update received:", updateData);
      console.log("Current user ID:", getCurrentUserId());
      console.log("Update user ID:", updateData.userId);

      // Only update if this is for our user or if it's a broadcast
      if (!updateData.userId || updateData.userId === getCurrentUserId()) {
        console.log("✅ Processing job list update for current user");
        handleRefreshJobs(false);
      } else {
        console.log("❌ Ignoring job list update - not for current user");
      }
    });

    // Add connection state change logging
    connection.onclose((error) => {
      console.log("SignalR connection closed:", error);
    });

    connection.onreconnecting((error) => {
      console.log("SignalR reconnecting:", error);
    });

    connection.onreconnected((connectionId) => {
      console.log("SignalR reconnected:", connectionId);
    });

    connection.start()
      .then(() => {
        console.log("SignalR connection established successfully");
        console.log("Connection ID:", connection.connectionId);
        console.log("Connection state:", connection.state);
        console.log("Current user ID:", getCurrentUserId());
        console.log("Token present:", !!token);

        // Test the connection and load initial jobs
        setTimeout(() => {
          connection.invoke("TestConnection").catch(err => {
            console.error("Test connection failed:", err);
          });

          // Load initial jobs when SignalR connection is established
          handleRefreshJobs(false);
        }, 1000);
      })
      .catch(err => {
        console.error("SignalR connection failed:", err);
        console.error("Error details:", err.message);
        console.error("Token present:", !!token);
      });

    // Note: We don't auto-load videos on startup anymore since the new API
    // requires user interaction with the Google Photos picker
    // Videos will be loaded when user clicks "Load from Google Photos"

    return () => {
      connection.stop();
    };
  }, [token]);



  const handleGoogleSignIn = async (credential: string) => {
    try {
      console.log('Received Google credential, sending to backend...');
      const response = await fetch('/api/auth/google-signin', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ credential })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Google Sign-In error:', errorText);
        showNotification('Authentication failed', 'error');
        return;
      }

      const data = await response.json();
      console.log('Google Sign-In success:', data);

      // Store the JWT token
      localStorage.setItem('jwt', data.token);
      setToken(data.token);
      showNotification('Successfully signed in!', 'success');
    } catch (err) {
      console.error('Error during Google Sign-In:', err);
      showNotification('Error during authentication', 'error');
    }
  };

  const handleGoogleOAuth = async () => {
    try {
      const response = await fetch('/api/auth/google-oauth-url');
      const data = await response.json();
      window.location.href = data.authUrl;
    } catch (err) {
      console.error('Error getting OAuth URL:', err);
      showNotification('Error starting Google authentication', 'error');
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('jwt');
    setToken(null);
    setMediaItems([]);
  };

  const handleCompress = (mediaItem: MediaItem) => {
    setCompressionTarget(mediaItem);
    setCompressionModalOpen(true);
  };

  const handleBatchCompress = () => {
    if (selectedItems.size === 0) {
      showNotification('No items selected for compression', 'error');
      return;
    }

    setCompressionTarget('batch');
    setCompressionModalOpen(true);
  };

  const executeCompression = async (mediaItem: MediaItem, settings: typeof compressionSettings) => {
    try {
      // Determine media type based on MIME type
      const isVideo = mediaItem.mimeType?.startsWith('video/') ?? false;
      const mediaType = isVideo ? 1 : 0; // Photo = 0, Video = 1 (enum values)

      const response = await fetch(`/api/media/${mediaItem.id}/compress`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          quality: settings.quality,
          mediaType: mediaType,
          uploadToGooglePhotos: settings.uploadToGooglePhotos,
          filename: mediaItem.filename,
          baseUrl: mediaItem.baseUrl,
          originalWidth: mediaItem.mediaMetadata?.width ? parseInt(mediaItem.mediaMetadata.width) : null,
          originalHeight: mediaItem.mediaMetadata?.height ? parseInt(mediaItem.mediaMetadata.height) : null,
          googlePhotosUrl: mediaItem.googlePhotosUrl
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to start compression: ${response.status}`);
      }

      const data = await response.json();
      console.log('Compression job queued:', data);

      // Immediately set the initial status
      setCompressionStatuses(prev => {
        const newMap = new Map(prev);
        newMap.set(mediaItem.id, {
          jobId: data.jobId,
          status: 'Queued',
          message: 'Queued for processing',
          progress: 0
        });
        return newMap;
      });

      // Auto-refresh jobs panel and open it when compression starts (only if user hasn't seen jobs yet)
      handleRefreshJobs();
      if (!hasSeenJobs) {
        setJobsPanelCollapsed(false);
        setHasSeenJobs(true);
      }

      return { success: true, jobId: data.jobId };
    } catch (err) {
      console.error('Error starting compression:', err);
      return { success: false, error: err };
    }
  };

  const handleConfirmCompression = async () => {
    setCompressionModalOpen(false);

    if (compressionTarget === 'batch') {
      const selectedMediaItems = mediaItems.filter(item => selectedItems.has(item.id));

      showNotification(`Starting compression for ${selectedItems.size} items...`, 'info');

      // Clear selection immediately when compression starts
      handleClearSelection();

      // Send all compression requests in parallel
      const compressionPromises = selectedMediaItems.map(mediaItem =>
        executeCompression(mediaItem, compressionSettings)
      );

      try {
        // Wait for all requests to complete (both successful and failed)
        const results = await Promise.allSettled(compressionPromises);

        let successCount = 0;
        let errorCount = 0;

        results.forEach((result) => {
          if (result.status === 'fulfilled' && result.value.success) {
            successCount++;
          } else {
            errorCount++;
          }
        });

        if (errorCount === 0) {
          showNotification(`Successfully started compression for ${successCount} items`, 'success');
        } else {
          showNotification(`Compression started for ${successCount} items, ${errorCount} failed`, 'error');
        }
      } catch (error) {
        console.error('Unexpected error during batch compression:', error);
        showNotification('Unexpected error during batch compression', 'error');
      }
    } else if (compressionTarget) {
      const result = await executeCompression(compressionTarget, compressionSettings);
      if (result.success) {
        showNotification(`Compression started for ${compressionTarget.filename}`, 'success');
      } else {
        showNotification('Error starting compression', 'error');
      }
    }

    setCompressionTarget(null);
  };

  const handleCancelCompression = () => {
    setCompressionModalOpen(false);
    setCompressionTarget(null);
  };

  const loadMediaItemsFromDatabase = async () => {
    if (!token) {
      return;
    }

    setLoadingPastMedia(true);
    try {
      const response = await fetch('/api/usermediaitems', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        if (response.status === 401) {
          // User not authenticated, ignore
          return;
        }
        throw new Error(`Failed to load media items: ${response.status}`);
      }

      const savedMediaItems = await response.json();
      console.log('Loaded media items from database:', savedMediaItems);

      // Convert saved media items back to the MediaItem format expected by the UI
      const convertedMediaItems: MediaItem[] = savedMediaItems.map((item: any) => ({
        id: item.googleMediaItemId,
        filename: item.filename,
        mimeType: item.mimeType,
        baseUrl: item.baseUrl,
        fileSizeBytes: item.fileSizeBytes,
        mediaMetadata: item.metadata ? JSON.parse(item.metadata) : {
          width: item.width?.toString() || '',
          height: item.height?.toString() || '',
          creationTime: item.creationTime || ''
        }
      }));

      // Update compression statuses from the loaded data
      const newCompressionStatuses = new Map<string, CompressionStatus>();
      savedMediaItems.forEach((item: any) => {
        if (item.latestCompressionJob) {
          const job = item.latestCompressionJob;
          // Ensure status is a string (handle both string and numeric enum values)
          const statusString = typeof job.status === 'string' ? job.status : getStatusFromEnum(job.status);

          newCompressionStatuses.set(item.googleMediaItemId, {
            jobId: job.id,
            status: statusString,
            message: job.errorMessage || getStatusMessage(statusString),
            progress: getProgressPercentage(statusString),
            error: job.errorMessage
          });
        }
      });

      setCompressionStatuses(newCompressionStatuses);
      setMediaItems(convertedMediaItems);

      if (convertedMediaItems.length > 0) {
        const jobCount = newCompressionStatuses.size;
        let message = `Restored ${convertedMediaItems.length} media items from your previous session`;
        if (jobCount > 0) {
          message += ` (${jobCount} with compression history)`;
        }
        showNotification(message, 'info');
      }
    } catch (error) {
      console.warn('Failed to load media items from database:', error);
      // Don't show error to user as this is a background operation
    } finally {
      setLoadingPastMedia(false);
    }
  };

  const saveMediaItemsToDatabase = async (mediaItems: MediaItem[]) => {
    if (!token) {
      throw new Error('No authentication token available');
    }

    const mediaItemRequests = mediaItems.map(item => ({
      googleMediaItemId: item.id,
      filename: item.filename,
      mimeType: item.mimeType,
      baseUrl: item.baseUrl,
      mediaType: item.mimeType?.startsWith('video/') ? 1 : 0, // 0 = Photo, 1 = Video
      width: item.mediaMetadata?.width ? parseInt(item.mediaMetadata.width) : null,
      height: item.mediaMetadata?.height ? parseInt(item.mediaMetadata.height) : null,
      fileSizeBytes: item.fileSizeBytes || estimateFileSize(item),
      creationTime: item.mediaMetadata?.creationTime ? new Date(item.mediaMetadata.creationTime).toISOString() : null,
      metadata: JSON.stringify(item.mediaMetadata)
    }));

    const response = await fetch('/api/usermediaitems/batch', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(mediaItemRequests)
    });

    if (!response.ok) {
      throw new Error(`Failed to save media items: ${response.status}`);
    }

    const savedItems = await response.json();
    console.log('Successfully saved media items to database:', savedItems);
    return savedItems;
  };

  const removeMediaItemFromDatabase = async (mediaItemId: string) => {
    if (!token) {
      throw new Error('No authentication token available');
    }

    // First, get the database ID for this Google media item ID
    const getResponse = await fetch(`/api/usermediaitems/by-google-id/${encodeURIComponent(mediaItemId)}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (!getResponse.ok) {
      throw new Error(`Failed to find media item: ${getResponse.status}`);
    }

    const item = await getResponse.json();

    // Now delete using the database ID
    const deleteResponse = await fetch(`/api/usermediaitems/${item.id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (!deleteResponse.ok) {
      throw new Error(`Failed to remove media item: ${deleteResponse.status}`);
    }

    console.log('Successfully removed media item from database:', mediaItemId);
  };

  const removeMultipleMediaItemsFromDatabase = async (mediaItemIds: string[]) => {
    if (!token) {
      throw new Error('No authentication token available');
    }

    // First, get the database IDs for these Google media item IDs
    const dbIds: string[] = [];
    for (const googleId of mediaItemIds) {
      try {
        const response = await fetch(`/api/usermediaitems/by-google-id/${encodeURIComponent(googleId)}`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        if (response.ok) {
          const item = await response.json();
          dbIds.push(item.id);
        }
      } catch (error) {
        console.warn(`Failed to get database ID for media item ${googleId}:`, error);
      }
    }

    if (dbIds.length === 0) {
      throw new Error('No valid media items found to delete');
    }

    const response = await fetch('/api/usermediaitems/batch', {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(dbIds)
    });

    if (!response.ok) {
      throw new Error(`Failed to remove media items: ${response.status}`);
    }

    const result = await response.json();
    console.log('Successfully removed media items from database:', result);
    return result;
  };

  const handleRemoveMediaItem = async (mediaItemId: string) => {
    try {
      await removeMediaItemFromDatabase(mediaItemId);

      // Remove from UI state
      setMediaItems(prevItems => prevItems.filter(item => item.id !== mediaItemId));
      setSelectedItems(prevSelected => {
        const newSelected = new Set(prevSelected);
        newSelected.delete(mediaItemId);
        return newSelected;
      });

      showNotification('Media item removed successfully', 'success');
    } catch (error) {
      console.error('Failed to remove media item:', error);
      showNotification('Failed to remove media item', 'error');
    }
  };

  const handleRemoveSelectedItems = async () => {
    if (selectedItems.size === 0) {
      showNotification('No items selected', 'info');
      return;
    }

    try {
      const selectedIds = Array.from(selectedItems);
      await removeMultipleMediaItemsFromDatabase(selectedIds);

      // Remove from UI state
      setMediaItems(prevItems => prevItems.filter(item => !selectedItems.has(item.id)));
      setSelectedItems(new Set());
      setLastSelectedIndex(null);

      showNotification(`Successfully removed ${selectedIds.length} media items`, 'success');
    } catch (error) {
      console.error('Failed to remove selected media items:', error);
      showNotification('Failed to remove selected media items', 'error');
    }
  };

  const handleSelectFromGooglePhotos = async () => {
    if (!token) return;

    try {
      setVideosLoading(true);
      setCancelPolling(false);
      cancelPollingRef.current = false;
      console.log('Initial cancelPolling state set to false');
      console.log('Creating picker session with token:', token.substring(0, 20) + '...');

      // Step 1: Create picker session
      const response = await fetch('/api/videos', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      console.log('Response status:', response.status);
      const responseText = await response.text();
      console.log('Response text:', responseText);

      if (!response.ok) {
        const errorData = responseText ? JSON.parse(responseText) : {};

        // Handle re-authentication requirement
        if (response.status === 403 && errorData.requiresReauth) {
          showNotification('Google Photos access requires re-authentication. Redirecting...', 'info');
          setTimeout(() => {
            window.location.href = errorData.authUrl;
          }, 2000);
          return;
        }

        throw new Error(`Failed to create picker session: ${response.status} - ${responseText}`);
      }

      const sessionData = responseText ? JSON.parse(responseText) : {};
      console.log('Picker session created:', sessionData);

      if (!sessionData.pickerUri) {
        throw new Error('No picker URI received from server');
      }

      // Step 2: Open picker URI in new window/tab
      showNotification('Google Photos picker opened! Select your photos and videos, then click "Done". Use the Cancel button below if you want to stop.', 'info');
      const pickerWindow = window.open(sessionData.pickerUri, '_blank', 'width=1000,height=700');

      // Note: Window closure detection is unreliable for cross-origin redirects like Google Photos
      // Users can manually cancel using the cancel button, or the session will timeout naturally

      // Step 3: Poll for session completion
      let pollCount = 0;
      let sessionStartTime = Date.now();
      let sessionTimeoutMs = 5 * 60 * 1000; // Default 5 minutes, will be updated from pollingConfig

      const pollSession = async () => {
        try {
          // Check if user cancelled
          if (cancelPollingRef.current) {
            console.log('Polling cancelled by user');
            showNotification('Photo selection cancelled', 'info');
            if (pickerWindow && !pickerWindow.closed) {
              pickerWindow.close();
            }
            setVideosLoading(false);
            return;
          }

          pollCount++;
          console.log(`Polling session status (attempt ${pollCount})`);

          const statusResponse = await fetch(`/api/videos/session/${sessionData.sessionId}/status`, {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });

          if (!statusResponse.ok) {
            throw new Error(`Failed to check session status: ${statusResponse.status}`);
          }

          const statusData = await statusResponse.json();
          console.log('Session status:', statusData);

          // Log polling configuration if available
          if (statusData.pollingConfig) {
            console.log(`Google recommends: poll every ${statusData.pollingConfig.pollInterval || 'default'}, timeout in ${statusData.pollingConfig.timeoutIn || 'default'}`);
          }

          // Update timeout from Google's pollingConfig on each response (it can change)
          if (statusData.pollingConfig?.timeoutIn) {
            const timeoutSeconds = parseFloat(statusData.pollingConfig.timeoutIn.replace('s', ''));

            // If timeoutIn is 0, Google is telling us to stop polling immediately
            if (timeoutSeconds === 0) {
              console.log('Google API indicates to stop polling (timeoutIn: 0s)');
              showNotification('Photo selection session expired. Please try again.', 'error');

              setVideosLoading(false);
              return;
            }

            sessionTimeoutMs = timeoutSeconds * 1000;
            if (pollCount === 1) {
              console.log(`Session timeout set to ${timeoutSeconds} seconds`);
            }
          }

          if (statusData.mediaItemsSet) {
            // Step 4: Get selected videos
            console.log('Media items selected! Fetching videos...');
            const videosResponse = await fetch(`/api/videos/session/${sessionData.sessionId}/videos`, {
              headers: {
                'Authorization': `Bearer ${token}`
              }
            });

            if (!videosResponse.ok) {
              throw new Error(`Failed to fetch selected videos: ${videosResponse.status}`);
            }

            const mediaData = await videosResponse.json();
            console.log('Selected media response:', mediaData);
            console.log('Debug info:', mediaData.debug);
            console.log('Total selected items:', mediaData.totalSelected);
            console.log('Video count:', mediaData.videoCount);
            console.log('Photo count:', mediaData.photoCount);
            console.log('MIME types found:', mediaData.debug?.mimeTypes);

            const loadedMediaItems = mediaData.mediaItems || [];

            // Add estimated file sizes to loaded media items for immediate display
            const mediaItemsWithSizes = loadedMediaItems.map((item: MediaItem) => ({
              ...item,
              fileSizeBytes: item.fileSizeBytes || estimateFileSize(item)
            }));

            // Merge new items with existing ones (avoid duplicates) - put new items at the beginning
            setMediaItems(prevItems => {
              const existingIds = new Set(prevItems.map((item: MediaItem) => item.id));
              const newItems = mediaItemsWithSizes.filter((item: MediaItem) => !existingIds.has(item.id));
              return [...newItems, ...prevItems];
            });

            // Save media items to database for persistence
            if (mediaItemsWithSizes.length > 0) {
              try {
                await saveMediaItemsToDatabase(mediaItemsWithSizes);
              } catch (saveError) {
                console.warn('Failed to save media items to database:', saveError);
                // Don't show error to user as the main functionality still works
              }
            }

            const totalItems = mediaData.totalSelected || 0;
            const videoCount = mediaData.videoCount || 0;
            const photoCount = mediaData.photoCount || 0;

            if (totalItems > 0) {
              let message = `Successfully loaded ${totalItems} items from Google Photos`;
              if (videoCount > 0 && photoCount > 0) {
                message += ` (${videoCount} videos, ${photoCount} photos)`;
              } else if (videoCount > 0) {
                message += ` (${videoCount} videos)`;
              } else if (photoCount > 0) {
                message += ` (${photoCount} photos)`;
              }
              showNotification(message, 'success');
            } else {
              showNotification('No items were selected', 'info');
            }

            // Step 5: Clean up session
            try {
              await fetch(`/api/videos/session/${sessionData.sessionId}`, {
                method: 'DELETE',
                headers: {
                  'Authorization': `Bearer ${token}`
                }
              });
              console.log('Session cleaned up successfully');
            } catch (cleanupErr) {
              console.warn('Failed to clean up session:', cleanupErr);
            }

            if (pickerWindow && !pickerWindow.closed) {
              pickerWindow.close();
            }
            setVideosLoading(false);
          } else {
            // Check if session has timed out based on Google's timeoutIn
            const elapsedTime = Date.now() - sessionStartTime;
            if (elapsedTime >= sessionTimeoutMs) {
              console.log(`Session timed out after ${elapsedTime}ms`);
              showNotification('Photo selection timed out. Please try again.', 'error');
              if (pickerWindow && !pickerWindow.closed) {
                pickerWindow.close();
              }
              setVideosLoading(false);
              return;
            }

            // Use Google's recommended polling interval (default to 1 second for faster response)
            let pollInterval = 1000; // Default 1 second
            if (statusData.pollingConfig?.pollInterval) {
              // Parse duration format like "3.5s" or "2s"
              const pollIntervalSeconds = parseFloat(statusData.pollingConfig.pollInterval.replace('s', ''));
              pollInterval = pollIntervalSeconds * 1000;
            }

            console.log(`Continuing to poll in ${pollInterval}ms... (${Math.round((sessionTimeoutMs - elapsedTime) / 1000)}s remaining)`);
            setTimeout(pollSession, pollInterval);
          }
        } catch (pollErr) {
          console.error('Error polling session:', pollErr);

          // Check if session has timed out
          const elapsedTime = Date.now() - sessionStartTime;
          if (elapsedTime >= sessionTimeoutMs) {
            console.log('Session timed out during error');
            showNotification('Photo selection timed out. Please try again.', 'error');
            setVideosLoading(false);
            return;
          }

          // For early errors, retry more quickly but with exponential backoff
          const retryDelay = pollCount <= 3 ? Math.min(1000 * pollCount, 3000) : 2000;
          console.log(`Retrying in ${retryDelay}ms due to error (attempt ${pollCount})`);

          // Only show error notification after several failed attempts
          if (pollCount > 5) {
            showNotification('Having trouble checking selection status, but still trying...', 'info');
          }

          setTimeout(pollSession, retryDelay);
        }
      };

      // Start polling immediately after a brief delay to let the picker load
      console.log('Starting polling in 2 seconds...');
      setTimeout(pollSession, 2000);

    } catch (err) {
      console.error('Error loading videos:', err);
      showNotification(`Error loading videos: ${err instanceof Error ? err.message : 'Unknown error'}`, 'error');
      setVideosLoading(false);
    }
  };

  // Jobs panel handlers
  const handleJobsPanelToggle = () => {
    setJobsPanelCollapsed(!jobsPanelCollapsed);
  };

  const handleRefreshJobs = async (showLoading = false) => {
    if (!token) {
      console.log("❌ Cannot refresh jobs - no token available");
      return;
    }

    console.log("🔄 Refreshing jobs...");
    // Note: showLoading parameter kept for compatibility but no longer used
    // since jobs panel updates via SignalR real-time
    try {
      const response = await fetch('/api/media/jobs?page=1&pageSize=50', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        const jobsData = Array.isArray(data) ? data : data.jobs || [];
        console.log("📋 Jobs data received:", jobsData);
        console.log("📊 Number of jobs:", jobsData.length);
        setJobs(jobsData);
      } else {
        console.error("❌ Failed to fetch jobs - response not ok:", response.status, response.statusText);
      }
    } catch (error) {
      console.error('❌ Error loading jobs:', error);
    }
  };

  // Remove auto-polling - jobs panel will be updated via SignalR

  const handleClearJob = async (jobId: string) => {
    if (!token) return;

    try {
      const response = await fetch(`/api/media/jobs/${jobId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        // Remove job from local state
        setJobs(prev => prev.filter(job => job.jobId !== jobId));
        showNotification('Job removed', 'success');
      } else {
        showNotification('Failed to remove job', 'error');
      }
    } catch (error) {
      console.error('Error removing job:', error);
      showNotification('Error removing job', 'error');
    }
  };

  const handleClearAllJobs = async () => {
    if (!token || jobs.length === 0) return;

    try {
      // Check how many jobs can be deleted (completed, failed, cancelled)
      const deletableJobs = jobs.filter(job =>
        ['completed', 'failed', 'cancelled'].includes(job.status.toLowerCase())
      );

      if (deletableJobs.length === 0) {
        showNotification('No jobs can be cleared (only completed/failed jobs can be removed)', 'info');
        return;
      }

      const response = await fetch('/api/media/jobs', {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const result = await response.json();
        const deletedCount = result.deletedCount || 0;

        // Remove deleted jobs from local state
        setJobs(prev => prev.filter(job =>
          !['completed', 'failed', 'cancelled'].includes(job.status.toLowerCase())
        ));

        if (deletedCount > 0) {
          showNotification(`Cleared ${deletedCount} jobs`, 'success');
        } else {
          showNotification('No jobs were cleared', 'info');
        }
      } else {
        showNotification('Failed to clear jobs', 'error');
      }
    } catch (error) {
      console.error('Error clearing jobs:', error);
      showNotification('Error clearing jobs', 'error');
    }
  };

  return (
    <ThemeProvider theme={theme}>
      <Router>
        <AppBar
          position="static"
          elevation={0}
          sx={{
            backgroundColor: 'grey.50',
            borderBottom: 1,
            borderColor: 'divider',
            color: 'text.primary',
            boxShadow: 'none'
          }}
        >
          <Toolbar sx={{ py: 1, boxShadow: '0 4px 12px rgba(0,0,0,0.15)' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
              <ThemeAwareLogo
                size={30}
                sx={{ mr: 1 }}
              />
              <Box sx={{ display: 'flex', flexGrow: 1, alignItems: 'flex-end' }}>
              <Typography
                variant="h6"
                component="div"
                sx={{
                  fontWeight: 300,
                  color: 'text.primary',
                  letterSpacing: '0px',
                  wordSpacing: '-2px',
                  mr: 1
                }}
              >
                Gallery Tuner
              </Typography>
              <Typography
                component="div"
                sx={{
                  fontSize: '14px',
                  fontWeight: 150,
                  color: 'text.primary',
                  letterSpacing: '0px',
                  wordSpacing: '0px',
                  ml: -0.3,
                  mb: 0.7
                }}
              >
                for Google Photos
              </Typography>
              </Box>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <ThemeSelector />
              {token && (
                <>
                  <Button
                    onClick={handleLogout}
                    startIcon={<Logout />}
                    sx={{
                      color: 'text.secondary',
                      textTransform: 'none',
                      fontWeight: 500,
                      '&:hover': {
                        backgroundColor: 'action.hover',
                        color: 'error.main'
                      },
                      borderRadius: 1
                    }}
                  >
                    Sign out
                  </Button>
                </>
              )}
            </Box>
          </Toolbar>
        </AppBar>
        <Box sx={{
          minHeight: '100vh',
          backgroundColor: 'background.default',
          pb: 4
        }}>
          <Container maxWidth={false} sx={{ pt: 6, px: { xs: 2, sm: 3, md: 4 } }}>
            <Snackbar
              open={!!notification}
              autoHideDuration={6000}
              onClose={() => setNotification(null)}
              anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
            >
              <Alert
                onClose={() => setNotification(null)}
                severity={notificationType}
                variant="filled"
                sx={{ width: '100%' }}
              >
                {notification}
              </Alert>
            </Snackbar>
            <Routes>
            <Route path="/" element={
              <Fade in timeout={800}>
                <Box>

                  {token && mediaItems.length > 0 && (
                    <Paper
                      elevation={0}
                      sx={{
                        p: 3,
                        mb: 2,
                        backgroundColor: 'grey.50',
                        border: 1,
                        borderColor: 'divider',
                        borderRadius: 2,
                        boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                      }}
                    >
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'baseline', flexWrap: 'wrap', gap: 2 }}>
                        {/* Left side: Load from Google Photos and Subscribe buttons */}
                        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
                          {videosLoading ? (
                            <Button
                              variant="contained"
                              size="large"
                              disabled={true}
                              sx={{
                                backgroundColor: 'action.disabledBackground',
                                color: 'action.disabled',
                                textTransform: 'none',
                                fontWeight: 500,
                                borderRadius: 1,
                                px: 3,
                                py: 1.5
                              }}
                            >
                              Waiting for media selection...
                            </Button>
                          ) : (
                            <GooglePhotosButton
                              onClick={handleSelectFromGooglePhotos}
                              size="large"
                            />
                          )}
                          {videosLoading && (
                            <Button
                              variant="outlined"
                              size="large"
                              onClick={() => {
                                setCancelPolling(true);
                                cancelPollingRef.current = true;
                              }}
                              sx={{
                                borderColor: 'error.main',
                                color: 'error.main',
                                textTransform: 'none',
                                fontWeight: 500,
                                '&:hover': {
                                  borderColor: 'error.dark',
                                  backgroundColor: 'error.light',
                                  color: 'error.dark'
                                },
                                borderRadius: 1,
                                px: 3,
                                py: 1.5
                              }}
                            >
                              Cancel
                            </Button>
                          )}
                          <Subscribe />
                        </Box>

                        {/* Right side: Sorting controls and item count */}
                        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
                          <GallerySortControls
                            sortBy={sortBy}
                            onSortChange={setSortBy}
                            totalItems={mediaItems.length}
                          />
                        </Box>
                      </Box>
                    </Paper>
                  )}

                  {/* Toolbar for when no media items are loaded */}
                  {token && mediaItems.length === 0 && (
                    <Paper
                      elevation={0}
                      sx={{
                        p: 3,
                        mb: 2,
                        backgroundColor: 'grey.50',
                        border: 1,
                        borderColor: 'divider',
                        borderRadius: 2,
                        boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                      }}
                    >
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: 2 }}>
                        {/* Left side: Load from Google Photos button */}
                        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
                          {videosLoading ? (
                            <Button
                              variant="contained"
                              size="large"
                              disabled={true}
                              sx={{
                                backgroundColor: 'action.disabledBackground',
                                color: 'action.disabled',
                                textTransform: 'none',
                                fontWeight: 500,
                                borderRadius: 1,
                                px: 3,
                                py: 1.5
                              }}
                            >
                              Waiting for media selection...
                            </Button>
                          ) : (
                            <GooglePhotosButton
                              onClick={handleSelectFromGooglePhotos}
                              size="large"
                            />
                          )}
                          {videosLoading && (
                            <Button
                              variant="outlined"
                              size="large"
                              onClick={() => {
                                setCancelPolling(true);
                                cancelPollingRef.current = true;
                              }}
                              sx={{
                                borderColor: 'error.main',
                                color: 'error.main',
                                textTransform: 'none',
                                fontWeight: 500,
                                '&:hover': {
                                  borderColor: 'error.dark',
                                  backgroundColor: 'error.light',
                                  color: 'error.dark'
                                },
                                borderRadius: 1,
                                px: 3,
                                py: 1.5
                              }}
                            >
                              Cancel
                            </Button>
                          )}
                          <Subscribe />
                        </Box>

                        {/* Right side: Filters placeholder */}
                        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
                          <Typography variant="body2" sx={{ color: 'text.secondary', fontStyle: 'italic' }}>
                            Filters
                          </Typography>
                        </Box>
                      </Box>
                    </Paper>
                  )}

                  {token ? (
                    <>
                      {/* Main Content Area with Sidebar Layout */}
                      <Box sx={{
                        display: 'flex',
                        gap: { xs: 2, md: 3 },
                        alignItems: 'flex-start',
                        width: '100%',
                        flexDirection: { xs: 'column', lg: 'row' }
                      }}>
                        {/* Operations Sidebar */}
                        <OperationsSidebar
                          selectedItems={selectedItems}
                          totalItems={mediaItems.length}
                          expiredItemsCount={expiredMediaItems.size}
                          onSelectAll={handleSelectAll}
                          onClearSelection={handleClearSelection}
                          onCompressSelected={handleBatchCompress}
                          onRemoveSelected={handleRemoveSelectedItems}
                          onClearExpired={handleClearAllExpired}
                          isCollapsed={sidebarCollapsed}
                          onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
                        />

                        {/* Content Area */}
                        <Box sx={{
                          flex: 1,
                          minWidth: 0,
                          width: '100%',
                          maxWidth: '100%'
                        }}>
                          {loadingPastMedia ? (
                            <Paper
                              elevation={0}
                              sx={{
                                p: 6,
                                textAlign: 'center',
                                backgroundColor: 'grey.50',
                                border: 1,
                                borderColor: 'divider',
                                borderRadius: 2
                              }}
                            >
                              <CircularProgress sx={{ mb: 2 }} />
                              <Typography variant="h5" gutterBottom sx={{ color: 'text.primary', fontWeight: 400 }}>
                                Loading your media
                              </Typography>
                              <Typography variant="body1" sx={{ color: 'text.secondary' }}>
                                Restoring media from previous sessions...
                              </Typography>
                            </Paper>
                          ) : mediaItems.length > 0 ? (
                            <>
                            {/* Instructions */}
                            {showInstructions && (
                              <Box sx={{
                                mb: 3,
                                textAlign: 'center',
                                position: 'relative',
                                backgroundColor: 'grey.50',
                                borderRadius: 1,
                                p: 2,
                                border: 1,
                                borderColor: 'divider'
                              }}>
                                <IconButton
                                  size="small"
                                  onClick={() => setShowInstructions(false)}
                                  sx={{
                                    position: 'absolute',
                                    top: '50%',
                                    right: 8,
                                    transform: 'translateY(-50%)',
                                    color: 'text.secondary'
                                  }}
                                >
                                  <Close fontSize="small" />
                                </IconButton>
                                <Typography variant="body2" sx={{ color: 'text.secondary', fontSize: '14px', pr: 4 }}>
                                  Click to select items • Shift+click to select range • Ctrl/Cmd+click to toggle selection
                                </Typography>
                              </Box>
                            )}

                        <Box
                          sx={{
                            display: 'grid',
                            gridTemplateColumns: {
                              xs: 'repeat(auto-fill, minmax(150px, 1fr))',
                              sm: 'repeat(auto-fill, minmax(180px, 1fr))',
                              md: 'repeat(auto-fill, minmax(200px, 1fr))',
                              lg: 'repeat(auto-fill, minmax(200px, 1fr))'
                            },
                            gap: 0,
                            width: '100%',
                            userSelect: 'none',
                            WebkitUserSelect: 'none',
                            MozUserSelect: 'none',
                            msUserSelect: 'none'
                          }}
                        >
                        {sortedMediaItems.map((mediaItem, index) => {
                          const isSelected = selectedItems.has(mediaItem.id);
                          const isExpired = expiredMediaItems.has(mediaItem.id);
                          return (
                            <Box
                              key={mediaItem.id || index}
                              onClick={(e) => handleItemClick(mediaItem, index, e)}
                              sx={{
                                position: 'relative',
                                aspectRatio: '1',
                                overflow: 'hidden',
                                cursor: 'pointer',
                                border: isSelected ? '3px solid' : isExpired ? '3px solid' : '3px solid transparent',
                                borderColor: isSelected ? 'primary.main' : isExpired ? 'warning.main' : 'transparent',
                                borderRadius: '7px',
                                transition: 'border-color 0.2s ease',
                                opacity: isExpired ? 0.7 : 1,
                                userSelect: 'none',
                                WebkitUserSelect: 'none',
                                MozUserSelect: 'none',
                                msUserSelect: 'none',
                                '&:hover .media-overlay': {
                                  opacity: 1
                                },
                                '&:hover .media-image': {
                                  transform: 'scale(1.05)'
                                },
                                '&:hover .selection-indicator': {
                                  opacity: 1
                                }
                              }}
                            >
                            {/* Media Image */}
                            {mediaItem.id && token ? (
                              <img
                                className="media-image"
                                src={`/api/videos/${mediaItem.id}/preview?width=400&height=400&crop=true&token=${encodeURIComponent(token)}&baseUrl=${encodeURIComponent(mediaItem.baseUrl)}`}
                                alt={mediaItem.filename}
                                style={{
                                  width: '100%',
                                  height: '100%',
                                  objectFit: 'cover',
                                  borderRadius: '4px',
                                  transition: 'transform 0.2s ease, opacity 0.3s ease',
                                  display: 'block',
                                  opacity: 0
                                }}
                                onLoad={(e) => {
                                  // Fade in the image once it's loaded
                                  const target = e.currentTarget;
                                  target.style.opacity = '1';
                                }}
                                onError={(e) => {
                                  // Mark this media item as expired and show appropriate message
                                  handleMediaExpired(mediaItem.id);

                                  const target = e.currentTarget;
                                  target.style.display = 'none';
                                  const parent = target.parentElement!;
                                  const fallbackDiv = document.createElement('div');
                                  fallbackDiv.className = 'media-image';
                                  fallbackDiv.style.cssText = `
                                    width: 100%;
                                    height: 100%;
                                    display: flex;
                                    flex-direction: column;
                                    align-items: center;
                                    justify-content: center;
                                    background-color: #fff3cd;
                                    color: #856404;
                                    border: 2px solid #ffeaa7;
                                    transition: transform 0.2s ease, opacity 0.3s ease;
                                    opacity: 0;
                                  `;
                                  fallbackDiv.innerHTML = `
                                    <span style="font-size: 32px;">⚠️</span>
                                    <span style="margin-top: 8px; font-size: 11px; text-align: center; font-weight: 500;">Access Expired</span>
                                    <span style="margin-top: 2px; font-size: 10px; text-align: center;">Google Photos URL expired</span>
                                  `;
                                  parent.appendChild(fallbackDiv);
                                  // Fade in the fallback
                                  setTimeout(() => {
                                    fallbackDiv.style.opacity = '1';
                                  }, 10);
                                }}
                              />
                            ) : (
                              <Fade in={true} timeout={300}>
                                <Box
                                  className="media-image"
                                  sx={{
                                    width: '100%',
                                    height: '100%',
                                    display: 'flex',
                                    flexDirection: 'column',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    backgroundColor: 'grey.100',
                                    color: 'text.secondary',
                                    transition: 'transform 0.2s ease'
                                  }}
                                >
                                  <Box sx={{ fontSize: '48px', mb: 1 }}>
                                    {mediaItem.mimeType?.startsWith('video/') ? '🎥' : '📷'}
                                  </Box>
                                  <Typography variant="body2" sx={{ fontSize: '12px' }}>
                                    Preview unavailable
                                  </Typography>
                                </Box>
                              </Fade>
                            )}

                            {/* Video Duration Badge */}
                            {mediaItem.mimeType?.startsWith('video/') && (
                              <Box
                                sx={{
                                  position: 'absolute',
                                  bottom: 8,
                                  right: 8,
                                  backgroundColor: 'rgba(0, 0, 0, 0.7)',
                                  color: 'white',
                                  padding: '2px 6px',
                                  borderRadius: '4px',
                                  fontSize: '12px',
                                  fontWeight: 500,
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: 0.5
                                }}
                              >
                                <PlayArrow sx={{ fontSize: 14 }} />
                                Video
                              </Box>
                            )}

                            {/* Compression Status Indicator */}
                            <CompressionStatusIndicator
                              mediaItemId={mediaItem.id}
                              status={compressionStatuses.get(mediaItem.id)}
                              showOnlyLoadingCircle={true}
                            />



                            {/* Selection Indicator */}
                            <Box
                              className="selection-indicator"
                              sx={{
                                position: 'absolute',
                                top: 8,
                                right: 8,
                                width: 24,
                                height: 24,
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                zIndex: 2,
                                opacity: isSelected ? 1 : 0,
                                transition: 'opacity 0.2s ease'
                              }}
                            >
                              {isSelected ? (
                                <CheckCircle
                                  sx={{
                                    fontSize: 24,
                                    color: 'primary.main',
                                    backgroundColor: 'background.paper',
                                    borderRadius: '50%'
                                  }}
                                />
                              ) : (
                                <CheckCircleOutline
                                  sx={{
                                    fontSize: 24,
                                    color: 'white',
                                    filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.5))'
                                  }}
                                />
                              )}
                            </Box>

                            {/* Hover Overlay */}
                            <Box
                              className="media-overlay"
                              sx={{
                                position: 'absolute',
                                top: 0,
                                left: 0,
                                right: 0,
                                bottom: 0,
                                background: 'linear-gradient(to bottom, rgba(0,0,0,0.3) 0%, rgba(0,0,0,0) 30%, rgba(0,0,0,0) 70%, rgba(0,0,0,0.5) 100%)',
                                opacity: isSelected ? 0.7 : 0,
                                transition: 'opacity 0.2s ease',
                                display: 'flex',
                                flexDirection: 'column',
                                justifyContent: 'space-between',
                                padding: 1
                              }}
                            >
                              {/* Top Info */}
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                                <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                                  {expiredMediaItems.has(mediaItem.id) && (
                                    <Chip
                                      label="Expired"
                                      size="small"
                                      sx={{
                                        backgroundColor: 'warning.main',
                                        color: 'warning.contrastText',
                                        fontWeight: 600,
                                        fontSize: '9px',
                                        height: 18,
                                        '& .MuiChip-label': {
                                          px: 0.5
                                        }
                                      }}
                                    />
                                  )}
                                  <Chip
                                    label={mediaItem.mimeType?.startsWith('video/') ? 'Video' : 'Photo'}
                                    size="small"
                                    sx={{
                                      backgroundColor: 'background.paper',
                                      color: mediaItem.mimeType?.startsWith('video/') ? 'primary.main' : 'success.main',
                                      fontWeight: 500,
                                      fontSize: '10px',
                                      height: 20,
                                      border: 1,
                                      borderColor: 'divider'
                                    }}
                                  />
                                  {mediaItem.fileSizeBytes && (
                                    <Chip
                                      label={formatFileSize(mediaItem.fileSizeBytes)}
                                      size="small"
                                      sx={{
                                        backgroundColor: 'background.paper',
                                        color: 'text.secondary',
                                        fontSize: '10px',
                                        height: 20,
                                        border: 1,
                                        borderColor: 'divider'
                                      }}
                                    />
                                  )}
                                </Box>
                              </Box>

                              {/* Bottom Info - Removed filename display */}
                            </Box>
                          </Box>
                          );
                        })}
                      </Box>
                            </>
                          ) : (
                      <Paper
                        elevation={0}
                        sx={{
                          p: 6,
                          textAlign: 'center',
                          backgroundColor: 'grey.50',
                          border: 1,
                          borderColor: 'divider',
                          borderRadius: 2
                        }}
                      >
                        <VideoLibrary sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                        <Typography variant="h5" gutterBottom sx={{ color: 'text.primary', fontWeight: 400 }}>
                          No media found
                        </Typography>
                        <Typography variant="body1" sx={{ color: 'text.secondary' }}>
                          Click "Load from Google Photos" to get started
                        </Typography>
                      </Paper>
                            )}
                          </Box>

                        {/* Jobs Panel */}
                        <JobsPanel
                          isCollapsed={jobsPanelCollapsed}
                          onToggleCollapse={handleJobsPanelToggle}
                          jobs={jobs}
                          onClearJob={handleClearJob}
                          onClearAllJobs={handleClearAllJobs}
                          mediaItems={mediaItems}
                          token={token}
                        />
                        </Box>
                      </>
                    ) : (
                    <Paper
                      elevation={0}
                      sx={{
                        p: 8,
                        textAlign: 'center',
                        backgroundColor: 'grey.50',
                        border: 1,
                        borderColor: 'divider',
                        borderRadius: 2
                      }}
                    >
                      <Box sx={{ mb: 3 }}>
                        <Box sx={{
                          display: 'inline-flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          width: 80,
                          height: 80,
                          borderRadius: '50%',
                          backgroundColor: 'grey.100',
                          mb: 2
                        }}>
                          <AccountCircle sx={{ fontSize: 48, color: 'text.secondary' }} />
                        </Box>
                      </Box>
                      <Typography variant="h4" gutterBottom sx={{ fontWeight: 400, color: 'text.primary' }}>
                        Welcome to Gallery Tuner
                      </Typography>
                      <Typography variant="body1" sx={{ color: 'text.secondary', mb: 4, maxWidth: 400, mx: 'auto', lineHeight: 1.6 }}>
                        Sign in with your Google account to access and adjust your media from Google Photos
                      </Typography>
                      <GoogleSignInButton
                        onSuccess={handleGoogleSignIn}
                        onError={(error) => {
                          console.error('Google Sign-In error:', error);
                          showNotification('Sign-in failed', 'error');
                        }}
                        size="large"
                        theme="outline"
                        text="signin_with"
                        width={300}
                      />
                    </Paper>
                  )}
                </Box>
              </Fade>
            } />
            <Route path="/auth/callback" element={<AuthCallback />} />
            <Route path="/success" element={
              <Paper
                elevation={0}
                sx={{
                  p: 6,
                  textAlign: 'center',
                  backgroundColor: 'background.paper',
                  border: 1,
                  borderColor: 'divider',
                  borderRadius: 2
                }}
              >
                <Typography variant="h4" sx={{ color: 'success.main', fontWeight: 400 }} gutterBottom>
                  Thanks for subscribing!
                </Typography>
                <Typography variant="body1" sx={{ color: 'text.secondary' }}>
                  You now have access to premium compression features.
                </Typography>
              </Paper>
            } />
            <Route path="/cancel" element={
              <Paper
                elevation={0}
                sx={{
                  p: 6,
                  textAlign: 'center',
                  backgroundColor: 'background.paper',
                  border: 1,
                  borderColor: 'divider',
                  borderRadius: 2
                }}
              >
                <Typography variant="h4" sx={{ color: 'error.main', fontWeight: 400 }} gutterBottom>
                  Subscription canceled
                </Typography>
                <Typography variant="body1" sx={{ color: 'text.secondary' }}>
                  You can subscribe again anytime to access premium features.
                </Typography>
              </Paper>
            } />
          </Routes>
          </Container>
        </Box>

        {/* Compression Settings Modal */}
        <Dialog
          open={compressionModalOpen}
          onClose={handleCancelCompression}
          maxWidth="sm"
          fullWidth
          sx={{
            '& .MuiDialog-paper': {
              borderRadius: 3,
              p: 2
            }
          }}
        >
          <DialogTitle sx={{ pb: 1 }}>
            <Typography variant="h5" component="div" sx={{ fontWeight: 500 }}>
              Compression Settings
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
              {compressionTarget === 'batch'
                ? `Configure settings for ${selectedItems.size} selected items`
                : `Configure settings for ${compressionTarget?.filename || 'this media item'}`
              }
            </Typography>
          </DialogTitle>

          <DialogContent sx={{ pt: 2 }}>
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 500 }}>
                Quality Settings
              </Typography>
              <FormControl fullWidth>
                <InputLabel>Compression Quality</InputLabel>
                <Select
                  value={compressionSettings.quality}
                  label="Compression Quality"
                  onChange={(e) => setCompressionSettings(prev => ({ ...prev, quality: e.target.value }))}
                >
                  <MenuItem value="high">
                    <Box>
                      <Typography variant="body1">High Quality</Typography>
                      <Typography variant="caption" color="text.secondary">
                        Better quality, larger file size
                      </Typography>
                    </Box>
                  </MenuItem>
                  <MenuItem value="medium">
                    <Box>
                      <Typography variant="body1">Medium Quality</Typography>
                      <Typography variant="caption" color="text.secondary">
                        Balanced quality and size
                      </Typography>
                    </Box>
                  </MenuItem>
                  <MenuItem value="low">
                    <Box>
                      <Typography variant="body1">Low Quality</Typography>
                      <Typography variant="caption" color="text.secondary">
                        Smaller file size, lower quality
                      </Typography>
                    </Box>
                  </MenuItem>
                </Select>
              </FormControl>
            </Box>

            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 500 }}>
                Options
              </Typography>

              {/* Upload to Google Photos Option */}
              <FormControlLabel
                control={
                  <Checkbox
                    checked={compressionSettings.uploadToGooglePhotos}
                    onChange={(e) => setCompressionSettings(prev => ({
                      ...prev,
                      uploadToGooglePhotos: e.target.checked
                    }))}
                  />
                }
                label={
                  <Box>
                    <Typography variant="body1">Upload compressed media to Google Photos</Typography>
                    <Typography variant="caption" color="text.secondary">
                      Save the compressed media back to your Google Photos library
                    </Typography>
                  </Box>
                }
                sx={{ mb: 2 }}
              />
            </Box>
          </DialogContent>

          <DialogActions sx={{ px: 3, pb: 2 }}>
            <Button onClick={handleCancelCompression} color="inherit">
              Cancel
            </Button>
            <Button
              onClick={handleConfirmCompression}
              variant="contained"
              sx={{
                borderRadius: 2,
                textTransform: 'none',
                fontWeight: 500
              }}
            >
              Start Compression
            </Button>
          </DialogActions>
        </Dialog>

      </Router>
    </ThemeProvider>
  );
};

// Main App component with theme provider
const App: React.FC = () => {
  return (
    <CustomThemeProvider>
      <AppContent />
    </CustomThemeProvider>
  );
};

export default App;
