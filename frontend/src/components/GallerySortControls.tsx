import React from 'react';
import {
  Box,
  Button,
  ButtonGroup,
  Typography,
  Paper
} from '@mui/material';
import {
  PhotoSizeSelectLarge,
  Schedule,
  SortByAlpha
} from '@mui/icons-material';

interface GallerySortControlsProps {
  sortBy: 'none' | 'size' | 'date';
  onSortChange: (sortBy: 'none' | 'size' | 'date') => void;
  totalItems: number;
}

const GallerySortControls: React.FC<GallerySortControlsProps> = ({
  sortBy,
  onSortChange,
  totalItems
}) => {
  if (totalItems === 0) {
    return null; // Don't show sorting controls when there are no items
  }

  return (
    <Paper
      elevation={1}
      sx={{
        p: 2,
        mb: 3,
        backgroundColor: 'background.paper',
        borderRadius: 2,
        border: '1px solid',
        borderColor: 'divider'
      }}
    >
      <Box sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        flexWrap: 'wrap',
        gap: 2
      }}>
        <Typography
          variant="subtitle1"
          sx={{
            fontWeight: 500,
            color: 'text.primary',
            display: 'flex',
            alignItems: 'center',
            gap: 1
          }}
        >
          Sort {totalItems} item{totalItems !== 1 ? 's' : ''} by:
        </Typography>

        <ButtonGroup
          variant="outlined"
          size="medium"
          sx={{
            '& .MuiButton-root': {
              textTransform: 'none',
              fontWeight: 500,
              px: 2,
              py: 1,
              borderRadius: 1,
              '&:first-of-type': {
                borderTopRightRadius: 0,
                borderBottomRightRadius: 0,
              },
              '&:last-of-type': {
                borderTopLeftRadius: 0,
                borderBottomLeftRadius: 0,
              },
              '&:not(:first-of-type):not(:last-of-type)': {
                borderRadius: 0,
              }
            }
          }}
        >
          <Button
            startIcon={<SortByAlpha />}
            onClick={() => onSortChange('none')}
            variant={sortBy === 'none' ? 'contained' : 'outlined'}
            sx={{
              backgroundColor: sortBy === 'none' ? 'primary.main' : 'transparent',
              color: sortBy === 'none' ? 'primary.contrastText' : 'text.primary',
              borderColor: 'divider',
              '&:hover': {
                backgroundColor: sortBy === 'none' ? 'primary.dark' : 'action.hover',
                borderColor: 'primary.main'
              }
            }}
          >
            Original Order
          </Button>

          <Button
            startIcon={<PhotoSizeSelectLarge />}
            onClick={() => onSortChange('size')}
            variant={sortBy === 'size' ? 'contained' : 'outlined'}
            sx={{
              backgroundColor: sortBy === 'size' ? 'primary.main' : 'transparent',
              color: sortBy === 'size' ? 'primary.contrastText' : 'text.primary',
              borderColor: 'divider',
              '&:hover': {
                backgroundColor: sortBy === 'size' ? 'primary.dark' : 'action.hover',
                borderColor: 'primary.main'
              }
            }}
          >
            File Size
          </Button>

          <Button
            startIcon={<Schedule />}
            onClick={() => onSortChange('date')}
            variant={sortBy === 'date' ? 'contained' : 'outlined'}
            sx={{
              backgroundColor: sortBy === 'date' ? 'primary.main' : 'transparent',
              color: sortBy === 'date' ? 'primary.contrastText' : 'text.primary',
              borderColor: 'divider',
              '&:hover': {
                backgroundColor: sortBy === 'date' ? 'primary.dark' : 'action.hover',
                borderColor: 'primary.main'
              }
            }}
          >
            Creation Date
          </Button>
        </ButtonGroup>
      </Box>
    </Paper>
  );
};

export default GallerySortControls;
